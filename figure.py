import matplotlib.pyplot as plt
import numpy as np
import pandas as pd

# 数据
data = {
    'Model': [
        'LLAMA-4-MAVERICK',
        'GPT-4.1',
        'OPEN AI O1',
        'OPEN AI O3',
        'CLAUDE-3.7-sonnet',
        'GEMINI-2.5-PRO',
        'GEMINI-2.5-FLASH',
        'Causal_MAS'
    ],
    'TRAIL (GAIA)': [ 0.023, 0.107, 0.040, 0.534, 0.204, 0.546, 0.372,0.446],
    'TRAIL (SWE Bench)': [0.083, 0.0, np.nan, np.nan, np.nan, 0.238, 0.06, 0.143]  # 负数设为0，CLE设为NaN
}

df = pd.DataFrame(data)

# 设置图表样式 - 适合论文双栏格式
plt.rcParams.update({
    'font.size': 8,
    'axes.titlesize': 9,
    'axes.labelsize': 8,
    'xtick.labelsize': 7,
    'ytick.labelsize': 7,
    'legend.fontsize': 7,
    'figure.titlesize': 10
})

# 创建图表 - 紧凑尺寸适合双栏
fig, ax = plt.subplots(figsize=(3.5, 2.8))  # 双栏论文的典型宽度

# 设置柱子位置和宽度
x = np.arange(len(df['Model']))
width = 0.35

# 绘制柱状图
bars1 = ax.bar(x - width/2, df['TRAIL (GAIA)'], width, 
               label='TRAIL (GAIA)', color='#FFA500', alpha=0.8)
bars2 = ax.bar(x + width/2, df['TRAIL (SWE Bench)'], width,
               label='TRAIL (SWE Bench)', color='#FF6B35', alpha=0.8)

# 设置标签和标题
ax.set_xlabel('Methonds', fontweight='bold')
ax.set_ylabel('Loc_acc Score', fontweight='bold')
ax.set_title('Model Performance Comparison', fontweight='bold', pad=10)

# 设置x轴标签
ax.set_xticks(x)

# 创建标签列表，为Causal_MAS_OURS添加特殊格式
labels = []
for model in df['Model']:
    if model == 'Causal_MAS_OURS':
        labels.append(model)
    else:
        labels.append(model)

ax.set_xticklabels(labels, rotation=45, ha='right')

# 为Causal_MAS添加背景高亮和加粗效果
for i, (tick, model) in enumerate(zip(ax.get_xticklabels(), df['Model'])):
    if model == 'Causal_MAS':
        # 设置加粗
        tick.set_fontweight('bold')
        tick.set_fontsize(8)
        # 添加背景框
        tick.set_bbox(dict(boxstyle="round,pad=0.3", facecolor='lightblue', alpha=0.7, edgecolor='blue'))

# 设置y轴范围
ax.set_ylim(0, 0.9)

# 添加网格
ax.grid(True, alpha=0.3, linestyle='--')
ax.set_axisbelow(True)

# 计算并添加平均线
gaia_mean = df['TRAIL (GAIA)'].mean()
swe_mean = np.nanmean(df['TRAIL (SWE Bench)'])

# 添加GAIA平均线
ax.axhline(y=gaia_mean, color='#FFA500', linestyle='--', linewidth=1, alpha=0.7,
           label=f'GAIA Average ({gaia_mean:.3f})')

# 添加SWE Bench平均线
ax.axhline(y=swe_mean, color='#FF6B35', linestyle='--', linewidth=1, alpha=0.7,
           label=f'SWE Bench Average ({swe_mean:.3f})')

# 添加图例 - 缩小并放在左上角，与图表上沿对齐
ax.legend(bbox_to_anchor=(0.00, 1), loc='upper left', frameon=True, fancybox=True, shadow=True, fontsize=6, framealpha=0.7)

# 紧凑布局
plt.tight_layout()

# 可选：为每个柱子添加数值标签
def add_value_labels(bars, values):
    for bar, value in zip(bars, values):
        if not np.isnan(value) and value > 0:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                   f'{value:.3f}', ha='center', va='bottom', fontsize=6)

# 添加数值标签（可选，根据需要取消注释）
add_value_labels(bars1, df['TRAIL (GAIA)'])
add_value_labels(bars2, df['TRAIL (SWE Bench)'])

# 保存图表
plt.savefig('model_performance_comparison.pdf', dpi=300, bbox_inches='tight')
plt.savefig('model_performance_comparison.png', dpi=300, bbox_inches='tight')

# 显示图表
plt.show()

# 打印一些统计信息
print("数据统计:")
print(f"GAIA数据集平均分: {df['TRAIL (GAIA)'].mean():.3f}")
print(f"SWE Bench数据集平均分: {np.nanmean(df['TRAIL (SWE Bench)']):.3f}")
print(f"最高GAIA分数: {df['TRAIL (GAIA)'].max():.3f} ({df.loc[df['TRAIL (GAIA)'].idxmax(), 'Model']})")
print(f"最高SWE Bench分数: {np.nanmax(df['TRAIL (SWE Bench)']):.3f} ({df.loc[df['TRAIL (SWE Bench)'].idxmax(), 'Model']})")